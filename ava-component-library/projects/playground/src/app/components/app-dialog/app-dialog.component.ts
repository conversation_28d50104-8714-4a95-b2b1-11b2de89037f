import { Component } from '@angular/core';
import { DialogService } from '../../../../../play-comp-library/src/lib/components/dialog/dialog-service';
import { ButtonComponent } from 'play-comp-library';


@Component({
  selector: 'app-app-dialog',
  imports: [ButtonComponent],
  providers: [DialogService],
  templateUrl: './app-dialog.component.html',
  styleUrl: './app-dialog.component.scss'
})
export class AppDialogComponent {
  constructor(private dialogService: DialogService) { }
  showSuccess() {
    this.dialogService.success();
  }
}
