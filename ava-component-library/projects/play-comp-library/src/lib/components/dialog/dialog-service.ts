import {
    ApplicationRef,
    ComponentRef,
    EmbeddedViewRef,
    Injectable,
    Injector,
    Type,
    createComponent,
    EnvironmentInjector
} from '@angular/core';
import { DialogContainerComponent } from './dialog-container/dialog-container.component'
import { SuccessComponent } from './success/success.component';


@Injectable({ providedIn: 'root' })
export class DialogService {
    private dialogRef!: ComponentRef<DialogContainerComponent>;
    constructor(
        private appRef: ApplicationRef,
        private injector: Injector,
        private envInjector: EnvironmentInjector
    ) { }

    open<T extends object>(component: Type<T>, data?: Partial<T>): Promise<any> {
        // Create container
        this.dialogRef = createComponent(DialogContainerComponent, {
            environmentInjector: this.envInjector,
            elementInjector: this.injector
        });

        this.appRef.attachView(this.dialogRef.hostView);
        const containerElem = (this.dialogRef.hostView as EmbeddedViewRef<any>)
            .rootNodes[0] as HTMLElement;
        document.body.appendChild(containerElem);

        // Create the target component dynamically inside the container
        const viewRef = this.dialogRef.instance.container.createComponent(component, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });

        if (data) {
            Object.assign(viewRef.instance, data);
        }

        return new Promise(resolve => {
            // Forward close from dialog shell
            this.dialogRef.instance.closed.subscribe((result: any) => {
                this.close();
                resolve(result);
            });

            // Optionally: Listen to `closed` output from the inner component
            const inner = viewRef.instance as any;
            if (inner.closed && typeof inner.closed.subscribe === 'function') {
                inner.closed.subscribe((result: any) => {
                    this.close();
                    resolve(result);
                });
            }
        });
    }

    close() {
        if (this.dialogRef) {
            this.appRef.detachView(this.dialogRef.hostView);
            this.dialogRef.destroy();
        }
    }
    success() {
        this.open(SuccessComponent, {}).then(result => {
            console.log('Modal closed with:', result);
        });
    }


}
