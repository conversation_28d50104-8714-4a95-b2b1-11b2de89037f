/* =======================
   AVA TEXTBOX - TEXT INPUT SPECIFICATIONS ONLY
   Implements only spec-compliant effects per design system
   ======================= */

/* =======================
   COMPONENT STYLES
   ======================= */

.ava-textbox {
  display: flex;
  flex-direction: column;
  gap: var(--textbox-gap);
  width: 100%;

  &--full-width {
    width: 100%;
  }

  // Size variants
  &--sm {
    gap: var(--textbox-gap-sm);
  }

  &--lg {
    gap: var(--textbox-gap-lg);
  }
}

.ava-textbox__label {
  display: block;
  font: var(--textbox-label-font);
  color: var(--textbox-label-color);
  margin-bottom: var(--textbox-label-margin);
  font-weight: var(--textbox-label-weight);

  &--required::after {
    content: "";
  }
}

.ava-textbox__required {
  color: var(--textbox-required-color);
  margin-left: 0.25rem;
}

.ava-textbox__container {
  position: relative;
  display: flex;
  align-items: center;

  // Default effect color for focus border and other effects (fallback)
  --effect-color-primary: var(--rgb-brand-primary);

  // Sophisticated glass effect - chained from base tokens
  background: var(--textbox-glass-default-background);
  backdrop-filter: var(--textbox-glass-default-blur);
  -webkit-backdrop-filter: var(--textbox-glass-default-blur);
  border: var(--textbox-glass-default-border);
  border-radius: var(--textbox-border-radius);

  // Default shadows from base tokens
  box-shadow: var(--textbox-glass-default-shadow);

  // Sophisticated transitions
  transition: var(--textbox-transition);

  // Hover effect - border color and width change
  &:hover:not(:focus-within) {
    border-color: rgba(var(--effect-color-primary), 1);
    border-width: 1.5px;
  }

  // Text Input Spec: Focus with thick border stroke (default)
  &:focus-within {
    border: 2px solid rgb(var(--effect-color-primary)) !important;
    box-shadow: 0 0 0 1px rgba(var(--effect-color-primary), 0.2),
      var(--textbox-glass-default-shadow);
    outline: none;
  }

  // Active/Pressed effect - chained from global pressed tokens
  &:active {
    box-shadow: var(--textbox-glass-default-shadow),
      var(--textbox-pressed-effect);
    transform: var(--textbox-pressed-transform);
  }

  // State styles
  .ava-textbox--disabled & {
    background: var(--textbox-background-disabled);
    border-color: var(--textbox-border-disabled-color);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .ava-textbox--readonly & {
    background: var(--textbox-background-readonly);
    border-color: var(--textbox-border-readonly-color);
  }

  // Text Input Spec: Grey disabled state only
  .ava-textbox--disabled-grey &,
  .ava-textbox--disabled-gray & {
    background: rgba(156, 163, 175, 0.3); // gray-400 with transparency
    border-color: rgba(156, 163, 175, 0.5);
    cursor: not-allowed;
    opacity: 0.7;
    filter: grayscale(100%);
  }

  // Sophisticated Variant Styles - Override glass-surface-color for different tints

  // Default variant - Explicit default variant support
  .ava-textbox--default & {
    --glass-surface-color: var(--textbox-variant-default);
    --effect-color-primary: var(--rgb-brand-primary);
  }

  // Primary variant - Override glass surface color to primary
  .ava-textbox--primary & {
    --glass-surface-color: var(--textbox-variant-primary);
    --effect-color-primary: var(--glass-variant-primary);
  }

  // Success variant - Override glass surface color to success
  .ava-textbox--success & {
    --glass-surface-color: var(--textbox-variant-success);
    --effect-color-primary: var(--glass-variant-success);
  }

  // Warning variant - Override glass surface color to warning
  .ava-textbox--warning & {
    --glass-surface-color: var(--textbox-variant-warning);
    --effect-color-primary: var(--glass-variant-warning);
  }

  // Danger/Error variant - Override glass surface color to danger
  .ava-textbox--danger &,
  .ava-textbox--error & {
    --glass-surface-color: var(--textbox-variant-danger);
    --effect-color-primary: var(--glass-variant-danger);
  }

  // Info variant - Override glass surface color to info
  .ava-textbox--info & {
    --glass-surface-color: var(--textbox-variant-info);
    --effect-color-primary: var(--glass-variant-info);
  }
}

.ava-textbox__input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font: var(--textbox-input-font);
  color: var(--textbox-input-color);
  padding: var(--textbox-input-padding);
  min-height: var(--textbox-input-min-height);
  line-height: 1.5;
  resize: none;
  font-weight: var(--textbox-label-weight);
  z-index: 2; // Above any overlay effects

  &::placeholder {
    color: var(--textbox-placeholder-color);
    opacity: 1;
  }

  &:disabled {
    color: var(--textbox-input-disabled-color);
    cursor: not-allowed;
  }

  &:read-only {
    color: var(--textbox-input-readonly-color);
    cursor: default;
  }

  // Size variants
  &--sm {
    padding: var(--textbox-input-padding-sm);
    min-height: var(--textbox-input-min-height-sm);
    font-size: var(--textbox-input-font-size-sm);
  }

  &--md {
    padding: var(--textbox-input-padding-md);
    min-height: var(--textbox-input-min-height-md);
    font-size: var(--textbox-input-font-size-md);
  }

  &--lg {
    padding: var(--textbox-input-padding-lg);
    min-height: var(--textbox-input-min-height-lg);
    font-size: var(--textbox-input-font-size-lg);
  }

  // Icon spacing classes are now applied dynamically based on projected content

  // Legacy icon spacing classes (kept for backward compatibility)
  &--icon-start {
    padding-left: var(--textbox-input-icon-padding-start);
  }

  &--icon-end {
    padding-right: var(--textbox-input-icon-padding-end);
  }

  // Additional spacing when both icons and prefix/suffix are present
  &--icon-start.ava-textbox__input--with-prefix {
    padding-left: calc(var(--textbox-input-icon-padding-start) + var(--textbox-separator-margin));
  }

  &--icon-end.ava-textbox__input--with-suffix {
    padding-right: calc(var(--textbox-input-icon-padding-end) + var(--textbox-separator-margin));
  }

  &--full-width {
    width: 100%;
  }
}

.ava-textbox__icons {
  display: flex;
  align-items: center;
  gap: var(--textbox-separator-margin);
  height: 100%;
  z-index: 3; // Above input and effects

  // Hide empty icon containers
  &:empty {
    display: none;
  }

  &--start {
    position: absolute;
    left: var(--textbox-icon-position-start);
    color: var(--textbox-icon-color);
    margin-right: var(--textbox-separator-margin); // Normal spacing between icon and text (default)
  }

  &--end {
    position: absolute;
    right: var(--textbox-icon-position-end);
    color: var(--textbox-icon-color);
    margin-left: var(--textbox-separator-margin); // Normal spacing between icon and text (default)
  }

  ava-icon {
    cursor: pointer;
    transition: var(--textbox-liquid-transition);

    &:hover,
    &:focus {
      color: var(--textbox-icon-focus-color);
      transform: var(--textbox-liquid-transform-hover);
    }

    &:active {
      opacity: var(--textbox-icon-active-opacity);
      transform: scale(0.95);
    }
  }

  .ava-textbox--disabled &,
  .ava-textbox--readonly & {
    color: var(--textbox-icon-disabled-color);
    cursor: not-allowed;
  }

  .ava-textbox--focused & {
    color: var(--textbox-icon-focus-color);
  }
}

.ava-textbox__prefix,
.ava-textbox__suffix {
  display: flex;
  align-items: center;
  padding: var(--textbox-affix-padding);
  color: var(--textbox-affix-color);
  font-size: var(--textbox-affix-font-size);
  background: var(--textbox-affix-background);
  border-radius: var(--textbox-affix-border-radius);
  z-index: 2;
  white-space: nowrap; // Prevent wrapping of prefix/suffix text

  // Hide empty prefix/suffix containers
  &:empty {
    display: none;
    padding: 0;
    margin: 0;
  }

  .ava-textbox--disabled & {
    color: var(--textbox-affix-disabled-color);
    background: var(--textbox-affix-disabled-background);
  }
}

.ava-textbox__prefix {
  border-top-left-radius: var(--textbox-border-radius);
  border-bottom-left-radius: var(--textbox-border-radius);
  margin-right: var(--textbox-affix-margin-normal); // Minimal spacing between prefix and input text

  &:empty {
    margin-right: 0;
  }
}

.ava-textbox__suffix {
  border-top-right-radius: var(--textbox-border-radius);
  border-bottom-right-radius: var(--textbox-border-radius);
  margin-left: var(--textbox-affix-margin-normal); // Minimal spacing between suffix and input text

  &:empty {
    margin-left: 0;
  }
}

// Icon Separator Styles
.ava-textbox__icon-separator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: var(--textbox-separator-width);
  height: var(--textbox-separator-height);
  background: var(--textbox-separator-background);
  z-index: 3;
  transition: var(--textbox-separator-transition);
  border-radius: 0.5px; // Slightly rounded edges for better visibility

  &--start {
    /* Increase gap between icon and separator */
    left: calc(var(--textbox-icon-position-start) + 24px + 0.5rem);
    /* 24px icon + 0.5rem gap */
  }

  &--end {
    right: calc(var(--textbox-icon-position-end) + 24px + 0.5rem);
  }

  // State-specific separator colors
  .ava-textbox--focused & {
    background: var(--textbox-separator-background-focused);
  }

  .ava-textbox--disabled & {
    background: var(--textbox-separator-background-disabled);
    opacity: var(--textbox-separator-opacity-disabled);
  }
}

.ava-textbox__icons--start {
  margin-right: 0.5rem;
  /* Add margin to push icon away from separator */
}

.ava-textbox__icons--end {
  margin-left: 0.5rem;
}

// Icon Separator Input Padding Adjustments
.ava-textbox--icon-separator {
  .ava-textbox__input--icon-start {
    /* Increase padding-left to add more space between separator and text */
    padding-left: calc(var(--textbox-icon-position-start) + 24px + 0.5rem + var(--textbox-separator-width) + 1.25rem) !important;
  }

  .ava-textbox__input--icon-end {
    padding-right: calc(var(--textbox-icon-position-end) + 24px + 0.5rem + var(--textbox-separator-width) + 0.5rem) !important;
  }
}

.ava-textbox__error {
  display: flex;
  align-items: flex-start;
  gap: var(--textbox-error-gap);
  color: var(--textbox-error-color);
  font-size: var(--textbox-error-font-size);
  line-height: 1.4;
}

.ava-textbox__error-icon {
  flex-shrink: 0;
  margin-top: var(--global-spacing-1);
}

.ava-textbox__error-text {
  flex: 1;
}

.ava-textbox__helper {
  display: flex;
  align-items: flex-start;
  gap: var(--textbox-helper-gap);
  color: var(--textbox-helper-color);
  font-size: var(--textbox-helper-font-size);
  line-height: 1.4;
}

.ava-textbox__helper-icon {
  flex-shrink: 0;
  margin-top: var(--global-spacing-1);
}

.ava-textbox__helper-text {
  flex: 1;
}

// Icon Spacing Variants
.ava-textbox--icon-spacing-compact {

  // Adjust input padding for compact icon spacing - creates visual gap between icon and text
  .ava-textbox__input--icon-start {
    padding-left: var(--textbox-icon-spacing-compact-padding-start) !important;
  }

  .ava-textbox__input--icon-end {
    padding-right: var(--textbox-icon-spacing-compact-padding-end) !important;
  }

  // Reduce prefix/suffix spacing for website and price variants
  .ava-textbox__prefix {
    margin-right: var(--textbox-affix-margin-compact) !important;
  }

  .ava-textbox__suffix {
    margin-left: var(--textbox-affix-margin-compact) !important;
  }
}

.ava-textbox--icon-spacing-relaxed {

  // Adjust input padding for relaxed icon spacing - creates visual gap between icon and text
  .ava-textbox__input--icon-start {
    padding-left: var(--textbox-icon-spacing-relaxed-padding-start) !important;
  }

  .ava-textbox__input--icon-end {
    padding-right: var(--textbox-icon-spacing-relaxed-padding-end) !important;
  }
}

// Normal spacing (explicit for clarity)
.ava-textbox--icon-spacing-normal {
  .ava-textbox__input--icon-start {
    padding-left: var(--textbox-icon-spacing-normal-padding-start) !important;
  }

  .ava-textbox__input--icon-end {
    padding-right: var(--textbox-icon-spacing-normal-padding-end) !important;
  }
}

/* =======================
     PLAY+ METAPHOR KEYFRAMES AND ANIMATIONS
     Component-owned animations and keyframes
     ======================= */

// Removed custom keyframes - using animate.css instead

/* =======================
     SOPHISTICATED GLASS VARIANTS - Props-Based Classes
     Each glass variant uses the sophisticated chained tokens
     ======================= */

// Glass Variant Classes - Text Input Spec Only
.ava-textbox--glass-10 .ava-textbox__container {
  background: var(--glass-10-gradient);
  background-color: #fff;
  backdrop-filter: var(--glass-10-blur);
  -webkit-backdrop-filter: var(--glass-10-blur);
  border: var(--glass-10-border);
  box-shadow: var(--glass-10-shadow);

  // Ensure focus border overrides glass border
  &:focus-within {
    border: 2px solid rgb(var(--effect-color-primary)) !important;
    box-shadow: 0 0 0 1px rgba(var(--effect-color-primary), 0.2),
      var(--textbox-glass-default-shadow);
  }
}

.ava-textbox--glass-50 .ava-textbox__container {
  // background: var(--glass-50-gradient);
  background: #fff;
  backdrop-filter: var(--glass-50-blur);
  -webkit-backdrop-filter: var(--glass-50-blur);
  border: var(--glass-50-border);
  box-shadow: var(--glass-50-shadow);

  // Ensure focus border overrides glass border
  &:focus-within {
    border: 2px solid rgb(var(--effect-color-primary)) !important;
    box-shadow: 0 0 0 1px rgba(var(--effect-color-primary), 0.2),
      var(--textbox-glass-default-shadow);
  }
}

/* =======================
     SOPHISTICATED EFFECTS IMPLEMENTATION - Using global effect tokens
     These can be applied via props: hoverEffect, pressedEffect, processingEffect
     ======================= */

// Hover Effects - Text Input Spec Only (Border color and width change)
.ava-textbox--hover-tint .ava-textbox__container:hover:not(:focus-within),
.ava-textbox--hover-glow .ava-textbox__container:hover:not(:focus-within) {
  border-color: rgba(var(--effect-color-primary), 1);
  border-width: 1px;
}

// Pressed Effects - Text Input Spec Only
.ava-textbox--pressed-solid .ava-textbox__container:active {
  // background: rgba(var(--effect-color-primary), 0.05);
  border-color: rgba(var(--effect-color-primary), 0.15);
  transform: scale(0.98);
  transition: all 0.1s ease;
}

// Processing Effects - Text Input Spec Only
.ava-textbox--processing-border-pulse .ava-textbox__container {
  animation: ava-border-pulse 2s ease-in-out infinite;
}

.ava-textbox--processing-shimmer .ava-textbox__container {
  .ava-textbox__input {
    animation: ava-text-shimmer 2s ease-in-out infinite;
  }
}

// Processing Gradient Border Variant (Animated, Multi-Color, Border Only)
.ava-textbox--processing-gradient-border .ava-textbox__container {
  --processing-border-width: 2px;
  position: relative;
  z-index: 0;
  background: var(--textbox-glass-default-background, #1d1f20);
  border-radius: var(--textbox-border-radius);
  border: none;

  &::before {
    content: "";
    position: absolute;
    z-index: 1;
    pointer-events: none;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(60deg,
        var(--processing-gradient-colors,
          #e91e63,
          #fee140,
          #ff9800,
          #047857,
          #ff9800,
          #fee140,
          #e91e63,
          #e91e63));
    background-size: 300% 300%;
    animation: animatedgradient 3s ease alternate infinite;
    padding: var(--processing-border-width);
    box-sizing: border-box;
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }

  // Ensure the container content is above the border
  >* {
    position: relative;
    z-index: 2;
  }
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

// Text Input Spec: Border pulse animation using variant color
@keyframes ava-border-pulse {

  0%,
  100% {
    border-color: var(--textbox-glass-default-border-color,
        rgba(var(--effect-color-primary), 0.3));
    box-shadow: var(--textbox-glass-default-shadow),
      0 0 0 0 rgba(var(--effect-color-primary), 0);
  }

  50% {
    border-color: rgba(var(--effect-color-primary), 0.8);
    box-shadow: var(--textbox-glass-default-shadow),
      0 0 0 3px rgba(var(--effect-color-primary), 0.2);
  }
}

// Text Input Spec: Text shimmer animation
@keyframes ava-text-shimmer {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
    text-shadow: 0 0 8px rgba(var(--effect-color-primary), 0.3);
  }
}

.ava-textbox--processing-gradient-border .ava-textbox__container:focus-within {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}