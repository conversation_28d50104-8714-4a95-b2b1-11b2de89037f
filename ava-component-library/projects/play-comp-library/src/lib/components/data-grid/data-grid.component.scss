.ava-data-table-wrapper {
    table-layout: auto;

    .data-table-wrapper {
        position: relative;
        overflow-x: auto;
        width: 100%;

        .ava-data-table {
            table-layout: auto;
            width: max-content;
            min-width: 100%;
            border-collapse: collapse;
            font-family: var(--table-font-family-body);
            margin: 1rem 0;
            border: 1px solid var(--table-border);

            color: var(--color-text-primary);

            th,
            td {
                padding: .5rem 1rem;
                text-align: left;

                ava-icon {
                    margin-right: 1rem;
                }
            }

            thead {
                background: var(--table-background-color-even);
            }

            tbody tr {
                transition: background-color 0.3s ease;

                &:nth-child(odd) {
                    background-color: var(--table-background-color-odd);
                }

                &:nth-child(even) {
                    background: var(--table-background-color-even);
                }

                .cell-link {
                    color: inherit;
                    text-decoration: none;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }


        }


    }
}