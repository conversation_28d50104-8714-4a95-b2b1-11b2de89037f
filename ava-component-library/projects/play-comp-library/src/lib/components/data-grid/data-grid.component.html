<div class="ava-data-table-wrapper">
    <div class="data-table-wrapper">
        <table class="ava-data-table">
            <thead>
                <tr>
                    <ng-container *ngFor="let column of columns">
                        <th (click)="onSort(column)" [style.cursor]="column.sortable ? 'pointer' : 'default'">
                            <div>
                                <ng-container *ngIf="column.headerCellDef">
                                    <ng-container *ngTemplateOutlet="column.headerCellDef.templateRef"></ng-container>
                                </ng-container>
                                <ng-container>
                                    <span class="sort-icon" *ngIf="sortColumn === column.name">
                                        <ng-container [ngSwitch]="sortDirection">
                                            <ava-icon *ngSwitchCase="'asc'" iconName="move-up" iconSize="15"
                                                alt="Ascending" />
                                            <ava-icon *ngSwitchCase="'desc'" iconName="move-down" iconSize="15"
                                                alt="Descending" />
                                            <!-- Optional: no icon if no direction -->
                                        </ng-container>
                                    </span>
                                    <span class="sort-icon" *ngIf="!sortDirection && column.sortable">
                                        <ava-icon iconName="move-up" iconSize="15" alt="Ascending" />
                                    </span>
                                </ng-container>
                            </div>
                        </th>
                    </ng-container>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let row of sortedData; let i = index">
                    <ng-container *ngFor=" let column of columns">
                        <td>
                            <ng-container *ngIf="column.cellDef" [ngTemplateOutlet]="column.cellDef.templateRef"
                                [ngTemplateOutletContext]="{ $implicit: row ,index: i}"></ng-container>
                        </td>
                    </ng-container>
                </tr>
            </tbody>
        </table>
    </div>
</div>