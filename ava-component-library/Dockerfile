# Stage 1: Build the Angular library
FROM node:20-alpine AS lib-builder
WORKDIR /app
COPY . .
RUN npm install --production
RUN npm run build --prefix projects/play-comp-library

# Stage 2: Build the playground app (demo UI)
FROM node:20-alpine AS playground-builder
WORKDIR /app
COPY . .
RUN npm install
RUN npx ng build playground

# Stage 3: Serve the playground app on port 4200
FROM node:20-alpine
WORKDIR /app
COPY --from=playground-builder /app/dist/playground ./dist/playground
RUN npm install -g http-server
EXPOSE 4200
CMD ["http-server", "dist/playground/browser", "-p", "4200"]
